<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">OG图片生成测试</h1>
    
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium mb-2">GitHub用户名:</label>
        <input 
          v-model="testUsername" 
          type="text" 
          placeholder="输入GitHub用户名" 
          class="border rounded px-3 py-2 w-64"
        />
        <button 
          @click="testOgImageGeneration" 
          :disabled="isGenerating"
          class="ml-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ isGenerating ? '生成中...' : '测试生成OG图片' }}
        </button>
      </div>

      <div v-if="result" class="mt-6">
        <h2 class="text-xl font-semibold mb-2">测试结果:</h2>
        <div class="bg-gray-100 p-4 rounded">
          <p><strong>状态:</strong> {{ result.success ? '成功' : '失败' }}</p>
          <p v-if="result.url"><strong>图片URL:</strong> <a :href="result.url" target="_blank" class="text-blue-500 underline">{{ result.url }}</a></p>
          <p v-if="result.error"><strong>错误:</strong> {{ result.error }}</p>
        </div>
      </div>

      <div v-if="result?.url" class="mt-6">
        <h2 class="text-xl font-semibold mb-2">预览:</h2>
        <img :src="result.url" alt="Generated OG Image" class="border rounded max-w-md" />
      </div>
    </div>

    <!-- 测试用的分享卡片 -->
    <div v-if="showTestCard" class="fixed -top-[9999px] -left-[9999px] pointer-events-none">
      <div 
        class="w-[1200px] h-[630px] bg-white p-8 flex items-center justify-center"
        data-card-id="test-og-card"
      >
        <div class="text-center">
          <h1 class="text-4xl font-bold mb-4">{{ testUsername }}</h1>
          <p class="text-xl text-gray-600">GitHub Developer Profile</p>
          <div class="mt-8 text-lg">
            <p>Generated by DINQ</p>
            <p class="text-sm text-gray-500">{{ new Date().toLocaleDateString() }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import html2canvas from 'html2canvas-pro'
import { uploadFileToS3 } from '~/utils'

const testUsername = ref('octocat')
const isGenerating = ref(false)
const result = ref<any>(null)
const showTestCard = ref(false)

const testOgImageGeneration = async () => {
  if (!testUsername.value.trim()) {
    alert('请输入GitHub用户名')
    return
  }

  isGenerating.value = true
  result.value = null
  showTestCard.value = true

  try {
    // 等待DOM渲染
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 1000))

    const cardElement = document.querySelector('[data-card-id="test-og-card"]')
    if (!cardElement) {
      throw new Error('未找到测试卡片元素')
    }

    // 生成图片
    const canvas = await html2canvas(cardElement as HTMLElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true,
      allowTaint: true,
      // 移除固定尺寸，使用动态尺寸
    })

    // 转换为blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
    })

    // 上传到S3
    const fileName = `test-og-${testUsername.value}-${Date.now()}.png`
    const publicUrl = await uploadFileToS3(blob, 'image/png', fileName)

    result.value = {
      success: true,
      url: publicUrl,
      fileName
    }

  } catch (error: any) {
    console.error('测试失败:', error)
    result.value = {
      success: false,
      error: error.message
    }
  } finally {
    isGenerating.value = false
    showTestCard.value = false
  }
}
</script>
